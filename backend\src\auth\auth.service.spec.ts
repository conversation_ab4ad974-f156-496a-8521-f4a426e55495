import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { createMockRepository, mockUser } from '../test/test-utils';
import * as bcrypt from 'bcrypt';

jest.mock('bcrypt');

describe('AuthService', () => {
  let service: AuthService;
  let usersService: jest.Mocked<UsersService>;
  let jwtService: jest.Mocked<JwtService>;
  let userRepository: ReturnType<typeof createMockRepository>;

  beforeEach(async () => {
    userRepository = createMockRepository();
    const mockUsersService = {
      findByEmail: jest.fn().mockResolvedValue(mockUser),
      create: jest.fn().mockResolvedValue(mockUser),
    };
    const mockJwtService = {
      sign: jest.fn().mockReturnValue('jwt-token'),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: getRepositoryToken(User),
          useValue: userRepository,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get(UsersService);
    jwtService = module.get(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should register a new user', async () => {
    const registerDto = {
      email: '<EMAIL>',
      password: 'password',
      username: 'testuser',
      role: 'user',
    };

    (bcrypt.hash as jest.Mock).mockResolvedValueOnce('hashed_password');
    userRepository.create.mockReturnValueOnce(mockUser);
    userRepository.save.mockResolvedValueOnce(mockUser);

    const result = await service.register(registerDto);
    expect(result).toEqual(mockUser);
    expect(bcrypt.hash).toHaveBeenCalledWith('password', 10);
  });

  it('should validate a user', async () => {
    (bcrypt.compare as jest.Mock).mockResolvedValueOnce(true);
    const result = await service.validateUser('<EMAIL>', 'password');
    expect(result).toEqual(mockUser);
  });
});
