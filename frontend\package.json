{"name": "marketoclock-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo \"No tests specified\" && exit 0", "clean": "rimraf .next"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^6.10.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "next": "^15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "postcss": "^8.4.31", "prisma": "^6.10.0", "typescript": "^5.3.2"}}