{"name": "marketoclock", "version": "1.0.0", "description": "Market O'Clock - A modern multi-service e-commerce and marketplace platform", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"pnpm --filter backend dev\" \"pnpm --filter frontend dev\"", "dev:backend": "pnpm --filter backend dev", "dev:frontend": "pnpm --filter frontend dev", "build": "pnpm --filter backend build && pnpm --filter frontend build", "build:backend": "pnpm --filter backend build", "build:frontend": "pnpm --filter frontend build", "start": "concurrently \"pnpm --filter backend start\" \"pnpm --filter frontend start\"", "start:backend": "pnpm --filter backend start", "start:frontend": "pnpm --filter frontend start", "test": "pnpm --filter backend test && pnpm --filter frontend test", "test:backend": "pnpm --filter backend test", "test:frontend": "pnpm --filter frontend test", "lint": "pnpm --filter backend lint && pnpm --filter frontend lint", "lint:backend": "pnpm --filter backend lint", "lint:frontend": "pnpm --filter frontend lint", "clean": "pnpm --filter backend clean && pnpm --filter frontend clean", "install:all": "pnpm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}