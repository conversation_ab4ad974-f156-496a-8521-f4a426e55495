import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { AuthMiddleware } from './auth/auth.middleware';
import { CategoriesModule } from './categories/categories.module';
import { ProductsModule } from './products/products.module';
import { OrdersModule } from './orders/orders.module';
import { PaymentsModule } from './payments/payments.module';
import { NotificationsModule } from './notifications/notifications.module';
import { PostsModule } from './posts/posts.module';
import { MicroblogModule } from './microblog/microblog.module';
import { CommentsModule } from './comments/comments.module';
import { HealthModule } from './health/health.module';
// import { MongoDBModule } from './mongodb/mongodb.module';
// import { MongoDBSocialModule } from './mongodb/mongodb-social.module';
// import mongodbConfig from './config/mongodb.config';
// import { MongooseModule } from '@nestjs/mongoose';
// import * as Joi from 'joi'; // Uncomment if you decide to use Joi for validation

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      // load: [mongodbConfig], // Temporarily disabled - MongoDB not available
      // Joi validation commented out as per your update; uncomment and install Joi if needed later
      // validationSchema: Joi.object({
      //   DATABASE_HOST: Joi.string().required(),
      //   DATABASE_PORT: Joi.number().default(5432),
      //   DATABASE_USERNAME: Joi.string().required(),
      //   DATABASE_PASSWORD: Joi.string().required(),
      //   DATABASE_NAME: Joi.string().required(),
      //   JWT_SECRET: Joi.string().required(),
      //   JWT_EXPIRATION: Joi.string().default('1d'),
      // }),
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'default_jwt_secret_for_development',
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION', '1d'),
        },
      }),
      global: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres' as const, // Explicit type for TypeScript
        host: configService.get<string>('DATABASE_HOST', 'localhost'),
        port: configService.get<number>('DATABASE_PORT', 5432),
        username: configService.get<string>('DATABASE_USERNAME', 'postgres'),
        password: configService.get<string>('DATABASE_PASSWORD', 'AU110s/6081/2021PG'),
        database: configService.get<string>('DATABASE_NAME', 'postgres'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: true, // Temporarily set to true to update the schema
        logging: process.env.NODE_ENV === 'development', // Optional: Enable logging in dev
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false, // Optional: SSL settings for production
      }),
    }),
    UsersModule,
    AuthModule,
    CategoriesModule,
    ProductsModule,
    OrdersModule,
    PaymentsModule,
    NotificationsModule,
    PostsModule,
    MicroblogModule,
    CommentsModule,
    HealthModule,
    // MongoDBModule, // Temporarily disabled - MongoDB not available
    // MongoDBSocialModule, // Temporarily disabled - MongoDB not available
    // MongooseModule.forRootAsync({
    //   imports: [ConfigModule],
    //   useFactory: async () => ({
    //     uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/marketoclock',
    //   }),
    // }),
  ],
  controllers: [AppController],
  providers: [AppService, AuthMiddleware],
  exports: [ConfigModule] // Export ConfigModule if needed in other modules
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes('*');
  }
}