import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { CategoriesModule } from './categories/categories.module';
import { ProductsModule } from './products/products.module';
import { OrdersModule } from './orders/orders.module';
// import * as Joi from 'joi'; // Uncomment if you decide to use Joi for validation

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      // Joi validation commented out as per your update; uncomment and install Joi if needed later
      // validationSchema: Joi.object({
      //   DATABASE_HOST: Joi.string().required(),
      //   DATABASE_PORT: Joi.number().default(5432),
      //   DATABASE_USERNAME: Joi.string().required(),
      //   DATABASE_PASSWORD: Joi.string().required(),
      //   DATABASE_NAME: Joi.string().required(),
      //   JWT_SECRET: Joi.string().required(),
      //   JWT_EXPIRATION: Joi.string().default('1d'),
      // }),
    }),
    // TypeOrmModule.forRootAsync({
    //   imports: [ConfigModule],
    //   inject: [ConfigService],
    //   useFactory: (configService: ConfigService) => ({
    //     type: 'postgres' as const,
    //     host: configService.get<string>('DB_HOST', 'localhost'),
    //     port: configService.get<number>('DB_PORT', 5432),
    //     username: configService.get<string>('DB_USERNAME', 'postgres'),
    //     password: configService.get<string>('DB_PASSWORD', 'postgres'),
    //     database: configService.get<string>('DB_DATABASE', 'marketoclock'),
    //     entities: [__dirname + '/**/*.entity{.ts,.js}'],
    //     synchronize: configService.get<string>('NODE_ENV') === 'development',
    //     logging: configService.get<string>('NODE_ENV') === 'development',
    //     ssl: configService.get<string>('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
    //   }),
    // }),
    // UsersModule,
    // AuthModule,
    // CategoriesModule,
    // ProductsModule,
    // OrdersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
  exports: [ConfigModule],
})
export class AppModule {}