# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=marketoclock
DATABASE_URL=**************************************/marketoclock

# Backend
NODE_ENV=development
PORT=4000
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION=1d

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:4000

# Analytics
ANALYTICS_PORT=5000
ANALYTICS_ENV=development

# Redis (for caching)
REDIS_URL=redis://redis:6379

# Email (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-specific-password

# Storage (AWS S3 or similar)
STORAGE_PROVIDER=local
STORAGE_BUCKET=marketoclock
STORAGE_REGION=us-east-1
STORAGE_ACCESS_KEY=your-access-key
STORAGE_SECRET_KEY=your-secret-key

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_EMAIL_NOTIFICATIONS=true 